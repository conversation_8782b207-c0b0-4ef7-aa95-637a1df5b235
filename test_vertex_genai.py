#!/usr/bin/env python3
"""
Vertex AI GenAI SDK Integration Test

This script tests the new unified Google GenAI SDK integration with Vertex AI.
It validates both streaming and non-streaming responses using the modern client.

Usage:
    python test_vertex_genai.py

Environment Variables Required:
    VERTEX_PROJECT_ID: Your Google Cloud project ID
    VERTEX_LOCATION: Vertex AI location (default: us-central1)
    VERTEX_USE_GENAI_SDK: Set to 'true' to use the new GenAI SDK
"""

import asyncio
import json
import os
import sys
from typing import Dict, Any

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.vertex_client import VertexAIClient

class VertexGenAITester:
    """Test suite for the Vertex AI GenAI SDK client."""
    
    def __init__(self):
        self.project_id = os.getenv("VERTEX_PROJECT_ID")
        self.location = os.getenv("VERTEX_LOCATION", "us-central1")
        
        if not self.project_id:
            raise ValueError("VERTEX_PROJECT_ID environment variable is required")
        
        # Set GenAI SDK environment variables
        os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "true"
        os.environ["GOOGLE_CLOUD_PROJECT"] = self.project_id
        os.environ["GOOGLE_CLOUD_LOCATION"] = self.location
        
        print(f"🔧 Initializing Vertex AI GenAI SDK client...")
        print(f"   Project ID: {self.project_id}")
        print(f"   Location: {self.location}")
        
        try:
            self.client = VertexAIClient(
                project_id=self.project_id,
                location=self.location
            )
            print("✅ Client initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize client: {e}")
            raise
    
    def create_test_request(self, include_image: bool = False) -> Dict[str, Any]:
        """Create a test request in OpenAI format."""
        messages = [
            {
                "role": "system",
                "content": "You are a helpful AI assistant. Respond concisely and clearly."
            },
            {
                "role": "user",
                "content": "Hello! Can you tell me a short joke about programming?"
            }
        ]
        
        if include_image:
            # Add a multimodal message with image
            messages.append({
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "What do you see in this image?"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
                        }
                    }
                ]
            })
        
        return {
            "model": "claude-3-5-sonnet-20241022",
            "messages": messages,
            "max_tokens": 150,
            "temperature": 0.7,
            "stream": False
        }
    
    async def test_non_streaming_completion(self) -> bool:
        """Test non-streaming chat completion."""
        print("\n🧪 Testing non-streaming completion...")
        
        try:
            request = self.create_test_request()
            response = await self.client.create_chat_completion(request)
            
            # Validate response structure
            required_fields = ["id", "object", "created", "model", "choices", "usage"]
            for field in required_fields:
                if field not in response:
                    print(f"❌ Missing field: {field}")
                    return False
            
            # Check choices structure
            if not response["choices"] or len(response["choices"]) == 0:
                print("❌ No choices in response")
                return False
            
            choice = response["choices"][0]
            if "message" not in choice or "content" not in choice["message"]:
                print("❌ Invalid choice structure")
                return False
            
            content = choice["message"]["content"]
            print(f"✅ Response received: {content[:100]}...")
            print(f"   Model: {response['model']}")
            print(f"   Usage: {response['usage']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Non-streaming test failed: {e}")
            return False
    
    async def test_streaming_completion(self) -> bool:
        """Test streaming chat completion."""
        print("\n🧪 Testing streaming completion...")
        
        try:
            request = self.create_test_request()
            request["stream"] = True
            
            chunks_received = 0
            content_parts = []
            
            async for chunk_data in self.client.create_chat_completion_stream(request):
                if chunk_data.strip() == "data: [DONE]":
                    break
                
                if chunk_data.startswith("data: "):
                    chunk_json = chunk_data[6:].strip()
                    if chunk_json and chunk_json != "[DONE]":
                        try:
                            chunk = json.loads(chunk_json)
                            chunks_received += 1
                            
                            if "choices" in chunk and chunk["choices"]:
                                delta = chunk["choices"][0].get("delta", {})
                                if "content" in delta:
                                    content_parts.append(delta["content"])
                        except json.JSONDecodeError:
                            continue
            
            full_content = "".join(content_parts)
            print(f"✅ Streaming completed: {chunks_received} chunks received")
            print(f"   Content: {full_content[:100]}...")
            
            return chunks_received > 0 and len(full_content) > 0
            
        except Exception as e:
            print(f"❌ Streaming test failed: {e}")
            return False
    
    async def test_model_mapping(self) -> bool:
        """Test Claude model name mapping to Vertex AI models."""
        print("\n🧪 Testing model mapping...")
        
        test_models = [
            "claude-3-5-sonnet-20241022",
            "claude-3-5-haiku-20241022",
            "claude-3-opus-20240229",
            "claude-instant"
        ]
        
        try:
            for model in test_models:
                mapped_model = self.client._map_model_name(model)
                print(f"   {model} → {mapped_model}")
            
            print("✅ Model mapping test completed")
            return True
            
        except Exception as e:
            print(f"❌ Model mapping test failed: {e}")
            return False
    
    async def test_cancellation(self) -> bool:
        """Test request cancellation functionality."""
        print("\n🧪 Testing request cancellation...")
        
        try:
            request = self.create_test_request()
            request_id = "test-cancel-123"
            
            # Start a completion task
            task = asyncio.create_task(
                self.client.create_chat_completion(request, request_id=request_id)
            )
            
            # Wait a bit then cancel
            await asyncio.sleep(0.1)
            cancelled = self.client.cancel_request(request_id)
            
            if not cancelled:
                print("❌ Failed to cancel request")
                return False
            
            try:
                await task
                print("❌ Task should have been cancelled")
                return False
            except Exception:
                print("✅ Request cancellation successful")
                return True
                
        except Exception as e:
            print(f"❌ Cancellation test failed: {e}")
            return False
    
    async def run_all_tests(self) -> bool:
        """Run all tests and return overall success."""
        print("🚀 Starting Vertex AI GenAI SDK integration tests...")
        
        tests = [
            ("Model Mapping", self.test_model_mapping),
            ("Non-streaming Completion", self.test_non_streaming_completion),
            ("Streaming Completion", self.test_streaming_completion),
            ("Request Cancellation", self.test_cancellation),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results.append(result)
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"\n{status}: {test_name}")
            except Exception as e:
                print(f"\n❌ FAILED: {test_name} - {e}")
                results.append(False)
        
        success_count = sum(results)
        total_count = len(results)
        
        print(f"\n📊 Test Results: {success_count}/{total_count} tests passed")
        
        if success_count == total_count:
            print("🎉 All tests passed! Vertex AI GenAI SDK integration is working correctly.")
            return True
        else:
            print("⚠️  Some tests failed. Please check the configuration and try again.")
            return False

async def main():
    """Main test execution function."""
    try:
        tester = VertexGenAITester()
        success = await tester.run_all_tests()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        print("\n💡 Make sure you have:")
        print("   1. Set VERTEX_PROJECT_ID environment variable")
        print("   2. Configured Google Cloud authentication (gcloud auth application-default login)")
        print("   3. Enabled Vertex AI API in your Google Cloud project")
        print("   4. Installed google-genai package (pip install google-genai>=0.3.0)")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())