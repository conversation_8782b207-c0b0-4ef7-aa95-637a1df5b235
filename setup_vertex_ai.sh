#!/bin/bash

# Vertex AI Setup Script for Claude Code Proxy
# This script helps you set up Vertex AI integration

set -e

echo "🚀 Setting up Vertex AI integration for Claude Code Proxy"
echo "======================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if gcloud is installed
check_gcloud() {
    print_info "Checking Google Cloud CLI installation..."
    if command -v gcloud &> /dev/null; then
        print_success "Google Cloud CLI is installed"
        gcloud version
    else
        print_error "Google Cloud CLI is not installed"
        print_info "Please install it from: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
}

# Check authentication
check_auth() {
    print_info "Checking Google Cloud authentication..."
    if gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
        ACTIVE_ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)")
        print_success "Authenticated as: $ACTIVE_ACCOUNT"
    else
        print_warning "No active authentication found"
        print_info "Running authentication..."
        gcloud auth login
        gcloud auth application-default login
    fi
}

# Get or set project
setup_project() {
    print_info "Setting up Google Cloud project..."
    
    # Get current project
    CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")
    
    if [ -n "$CURRENT_PROJECT" ]; then
        print_info "Current project: $CURRENT_PROJECT"
        read -p "Do you want to use this project? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            PROJECT_ID=$CURRENT_PROJECT
        else
            read -p "Enter your Google Cloud Project ID: " PROJECT_ID
            gcloud config set project $PROJECT_ID
        fi
    else
        read -p "Enter your Google Cloud Project ID: " PROJECT_ID
        gcloud config set project $PROJECT_ID
    fi
    
    print_success "Using project: $PROJECT_ID"
}

# Enable required APIs
enable_apis() {
    print_info "Enabling required APIs..."
    
    APIS=(
        "aiplatform.googleapis.com"
        "compute.googleapis.com"
    )
    
    for api in "${APIS[@]}"; do
        print_info "Enabling $api..."
        gcloud services enable $api
        print_success "$api enabled"
    done
}

# Install Python dependencies
install_dependencies() {
    print_info "Installing Python dependencies..."
    
    if [ -f "requirements.txt" ]; then
        uv pip install -r requirements.txt
        print_success "Dependencies installed"
    else
        print_warning "requirements.txt not found, installing manually..."
        uv add google-cloud-aiplatform google-auth google-auth-oauthlib
        print_success "Google Cloud dependencies installed"
    fi
}

# Create .env file
create_env_file() {
    print_info "Creating .env configuration file..."
    
    # Get location preference
    echo "Available Vertex AI locations:"
    echo "  1. us-central1 (Iowa, USA)"
    echo "  2. us-east1 (South Carolina, USA)"
    echo "  3. us-west1 (Oregon, USA)"
    echo "  4. europe-west1 (Belgium)"
    echo "  5. asia-southeast1 (Singapore)"
    echo "  6. Custom location"
    
    read -p "Choose location (1-6): " -n 1 -r
    echo
    
    case $REPLY in
        1) LOCATION="us-central1" ;;
        2) LOCATION="us-east1" ;;
        3) LOCATION="us-west1" ;;
        4) LOCATION="europe-west1" ;;
        5) LOCATION="asia-southeast1" ;;
        6) read -p "Enter custom location: " LOCATION ;;
        *) LOCATION="us-central1" ;;
    esac
    
    # Create .env file
    cat > .env << EOF
# Claude Code Proxy Configuration - Vertex AI
# Provider configuration
PROVIDER=vertex

# Vertex AI Configuration
VERTEX_PROJECT_ID=$PROJECT_ID
VERTEX_LOCATION=$LOCATION

# Google Cloud Authentication (optional - uses default credentials if not set)
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json

# Server Configuration
PORT=8082
HOST=0.0.0.0

# Model Configuration (Vertex AI defaults)
BIG_MODEL=gemini-1.5-pro
SMALL_MODEL=gemini-1.5-flash

# Token Limits
MAX_TOKENS_LIMIT=8192
MIN_TOKENS_LIMIT=1

# Request Configuration
REQUEST_TIMEOUT=300
MAX_RETRIES=3

# Logging
LOG_LEVEL=INFO
EOF
    
    print_success ".env file created with Vertex AI configuration"
    print_info "Configuration:"
    echo "  Provider: vertex"
    echo "  Project ID: $PROJECT_ID"
    echo "  Location: $LOCATION"
}

# Test the setup
test_setup() {
    print_info "Testing Vertex AI setup..."
    
    if [ -f "test_vertex_ai.py" ]; then
        python test_vertex_ai.py
    else
        print_warning "test_vertex_ai.py not found, skipping automated test"
        print_info "You can manually test by starting the proxy server:"
        echo "  python start_proxy.py"
    fi
}

# Main setup flow
main() {
    echo
    print_info "This script will help you set up Vertex AI integration"
    print_info "Make sure you have a Google Cloud project with billing enabled"
    echo
    
    read -p "Continue with setup? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Setup cancelled"
        exit 0
    fi
    
    echo
    check_gcloud
    echo
    check_auth
    echo
    setup_project
    echo
    enable_apis
    echo
    install_dependencies
    echo
    create_env_file
    echo
    
    print_success "Vertex AI setup completed successfully! 🎉"
    echo
    print_info "Next steps:"
    echo "  1. Review the .env file and adjust settings if needed"
    echo "  2. Start the proxy server: python start_proxy.py"
    echo "  3. Test the integration: python test_vertex_ai.py"
    echo
    print_info "For more information, see the README.md file"
    
    read -p "Would you like to run the test now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo
        test_setup
    fi
}

# Run main function
main "$@"