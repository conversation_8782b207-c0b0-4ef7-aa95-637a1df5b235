## Development Guidelines

### Framework and Language
> Analyze the framework and language choices for this project, focusing on best practices and standardization.

**Framework Considerations:**
- Version Compatibility: Ensure all dependencies are compatible with the chosen framework version
- Feature Usage: Leverage framework-specific features rather than reinventing solutions
- Performance Patterns: Follow recommended patterns for optimal performance
- Upgrade Strategy: Plan for future framework updates with minimal disruption
- Importance Notes for Framework: 
	* **FastAPI Async Support**: 所有 AI 提供商客户端必须使用异步模式，避免阻塞事件循环
	* **Google Cloud SDK**: Vertex AI 集成依赖 Google Cloud 认证，确保正确配置 GOOGLE_APPLICATION_CREDENTIALS
	* **Multi-Provider Architecture**: 使用工厂模式管理不同 AI 提供商，保持代码解耦和可扩展性
	* **Stream Processing**: 流式响应处理需要特别注意错误处理和连接管理

**Language Best Practices:**
- Type Safety: Use strong typing where available to prevent runtime errors
- Modern Features: Utilize modern language features while maintaining compatibility
- Consistency: Apply consistent coding patterns throughout the codebase
- Documentation: Document language-specific implementations and workarounds

### Code Abstraction and Reusability
> During development, prioritize code abstraction and reusability to ensure modular and component-based functionality. Try to search for existing solutions before reinventing the wheel.
> List below the directory structure of common components, utility functions, and API encapsulations in the current project.


**Modular Design Principles:**
- Single Responsibility: Each module is responsible for only one functionality
- High Cohesion, Low Coupling: Related functions are centralized, reducing dependencies between modules
- Stable Interfaces: Expose stable interfaces externally while internal implementations can vary

**Reusable Component Library:**
```
root
- src/
    - core/
        - client_factory.py      // 多提供商客户端工厂，支持 OpenAI/Azure/Vertex AI
        - config.py             // 统一配置管理，支持环境变量和默认值
        - model_manager.py      // 模型映射和管理，Claude 模型到提供商模型的转换
        - logging.py            // 结构化日志记录
    - conversion/
        - request_converter.py   // Claude -> OpenAI 请求转换
        - response_converter.py  // OpenAI -> Claude 响应转换
        - vertex_converter.py    // Claude <-> Vertex AI 双向转换
    - models/
        - claude.py             // Claude API 数据模型
        - openai.py             // OpenAI API 数据模型
    - api/
        - endpoints.py          // 统一 API 端点，支持多提供商路由
```

### Coding Standards and Tools
**Code Formatting Tools:**
- [Black (>=23.0.0)](https://black.readthedocs.io/) // Python 代码格式化
- [isort (>=5.12.0)](https://pycqa.github.io/isort/) // Python 导入排序
- [mypy (>=1.0.0)](https://mypy.readthedocs.io/) // Python 静态类型检查
- [pytest (>=7.0.0)](https://pytest.org/) // Python 测试框架

**Naming and Structure Conventions:**
- Semantic Naming: 变量/函数名称应清楚表达其用途，如 `convert_claude_to_vertex` 而非 `convert_request`
- Consistent Naming Style: Python 使用 snake_case，类名使用 PascalCase
- Directory Structure: 按功能职责划分目录结构
  * `core/` - 核心业务逻辑和客户端管理
  * `conversion/` - 请求/响应格式转换
  * `api/` - HTTP 端点和路由
  * `models/` - 数据模型定义

### Frontend-Backend Collaboration Standards
**API Design and Documentation:**
- RESTful design principles
	* Use HTTP methods (GET, POST, PUT, DELETE) to represent operations
	...
- Timely interface documentation updates
	* Document API endpoints, parameters, and responses
	...
- Unified error handling specifications
	...
	

**Data Flow:**
- Clear frontend state management
	* Use a state management library (e.g., Redux, Pinia) for consistent state handling
	...
- Data validation on both frontend and backend
	* Validate data types and constraints
	...
- Standardized asynchronous operation handling
	* Use consistent API call patterns
	...

### Performance and Security
**Performance Optimization Focus:**
- 异步处理优化
	* 所有 AI 提供商调用使用异步客户端
	* 流式响应避免内存积累
	* 连接池管理和超时控制
- 请求处理优化
	* 最小化请求转换开销
	* 高效的错误处理和重试机制
	* 适当的请求限流和排队
- 资源管理
	* Google Cloud 认证缓存
	* 客户端连接复用
	* 内存使用监控

**Security Measures:**
- API 密钥保护
	* 环境变量存储敏感信息
	* 避免在日志中暴露 API 密钥
	* Google Cloud 服务账号安全管理
- 输入验证和过滤
	* 严格验证 Claude API 请求格式
	* 防止注入攻击和恶意输入
	* 文件上传安全检查（多模态功能）
- 访问控制
	* 基于环境变量的提供商访问控制
	* 请求来源验证和限制
	* 错误信息脱敏处理