## Claude Code Proxy - Multi-Provider Support

> A high-performance proxy server that translates Claude API requests to multiple AI providers including OpenAI, Azure OpenAI, and Google Vertex AI

> Enables seamless integration with Claude-compatible applications while leveraging different AI backends, supporting both streaming and non-streaming requests with full multimodal capabilities

> Production-ready with comprehensive Vertex AI integration, automated setup scripts, and extensive testing framework

> Individual developer project with community contributions

> Python/FastAPI-based async proxy server with Google Cloud AI Platform integration, supporting multiple authentication methods and provider-specific optimizations



## Dependencies (init from programming language specification like package.json, requirements.txt, etc.)

* fastapi[standard] (>=0.115.11): Modern async web framework for building APIs
* uvicorn (>=0.34.0): ASGI server for running FastAPI applications
* pydantic (>=2.0.0): Data validation and settings management using Python type annotations
* python-dotenv (>=1.0.0): Environment variable management from .env files
* openai (>=1.54.0): Official OpenAI Python client for API interactions
* google-cloud-aiplatform (>=1.38.0): Google Cloud AI Platform client for Vertex AI integration
* google-auth (>=2.17.0): Google authentication library for service account and OAuth flows
* google-auth-oauthlib (>=1.0.0): OAuth 2.0 client library for Google APIs
* pytest (>=7.0.0): Testing framework for unit and integration tests
* pytest-asyncio (>=0.21.0): Async support for pytest
* httpx (>=0.25.0): Modern HTTP client for testing and external API calls
* black (>=23.0.0): Code formatter for consistent Python style
* isort (>=5.12.0): Import sorting utility
* mypy (>=1.0.0): Static type checker for Python


## Development Environment

> Python 3.8+ required with async/await support
> Google Cloud CLI (gcloud) for Vertex AI authentication and project management
> Optional: UV package manager for faster dependency management
> Environment variables configuration via .env files
> Google Cloud Project with Vertex AI API enabled and proper IAM permissions
> Automated setup script (setup_vertex_ai.sh) for quick Vertex AI configuration
> No Makefile - uses Python scripts for startup and testing


## Structrue (init from project tree)

> It is essential to consistently refine the analysis down to the file level — this level of granularity is of utmost importance.

> If the number of files is too large, you should at least list all the directories, and provide comments for the parts you consider particularly important.

> In the code block below, add comments to the directories/files to explain their functionality and usage scenarios.

> if you think the directory/file is not important, you can not skip it, just add a simple comment to it.

> but if you think the directory/file is important, you should read the files and add more detail comments on it (e.g. add comments on the functions, classes, and variables. explain the functionality and usage scenarios. write the importance of the directory/file).
```
root
- .gitignore                    # Git ignore patterns
- .codelf/                      # Project documentation and metadata
    - project.md                # Project overview and structure documentation
    - _changelog.md             # Change history tracking
    - attention.md              # Important notes and attention points
- .env.example                  # Environment configuration template for OpenAI/Azure
- .env.vertex.example           # Environment configuration template for Vertex AI
- CLAUDE.md                     # Claude API compatibility documentation
- QUICKSTART.md                 # Quick start guide for users
- README.md                     # Main project documentation with multi-provider setup
- demo.png                      # Demo screenshot or diagram
- pyproject.toml                # Python project configuration and metadata
- requirements.txt              # Python dependencies including Google Cloud libraries
- setup_vertex_ai.sh            # **CRITICAL** Automated Vertex AI setup script with gcloud integration
- src/                          # Main source code directory
    - __init__.py               # Package initialization
    - api/                      # FastAPI endpoint definitions
        - endpoints.py          # **CORE** Main API routes (/v1/messages, /health, /test-connection) with multi-provider support
    - conversion/               # Request/response transformation modules
        - request_converter.py  # Claude to OpenAI request conversion
        - response_converter.py # OpenAI to Claude response conversion
        - vertex_converter.py   # **NEW** Claude to Vertex AI bidirectional conversion with multimodal support
    - core/                     # Core business logic and client management
        - client.py             # OpenAI/Azure client with async support and cancellation
        - client_factory.py     # **NEW** Factory pattern for creating provider-specific clients
        - config.py             # **ENHANCED** Multi-provider configuration with Vertex AI support
        - constants.py          # Application constants and enums
        - logging.py            # Centralized logging configuration
        - model_manager.py      # Model mapping and management logic
        - vertex_client.py      # **NEW** Vertex AI client with Google Cloud authentication
    - main.py                   # **CORE** FastAPI application entry point with provider routing
    - models/                   # Pydantic data models
        - claude.py             # Claude API request/response models
        - openai.py             # OpenAI API models
- start_proxy.py                # **CRITICAL** Main startup script for the proxy server
- test_cancellation.py          # Request cancellation testing
- test_vertex_ai.py             # **NEW** Comprehensive Vertex AI integration testing suite
- tests/                        # Unit and integration tests
    - test_main.py              # Main application tests
- uv.lock                       # UV package manager lock file
```
