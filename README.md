# Claude Code Proxy

A proxy server that enables **Claude Code** to work with multiple AI providers. Convert Claude API requests to various provider formats, allowing you to use OpenAI, Azure OpenAI, Google Vertex AI, and other providers through the Claude Code CLI.

![Claude Code Proxy](demo.png)

## Features

- **Full Claude API Compatibility**: Complete `/v1/messages` endpoint support
- **Multiple Provider Support**: OpenAI, Azure OpenAI, Google Vertex AI, local models (Ollama), and any OpenAI-compatible API
- **Smart Model Mapping**: Configure BIG and SMALL models via environment variables
- **Function Calling**: Complete tool use support with proper conversion
- **Streaming Responses**: Real-time SSE streaming support
- **Image Support**: Base64 encoded image input for all providers
- **Error Handling**: Comprehensive error handling and logging
- **Multi-Modal Support**: Text and image processing across providers

## Quick Start

### 1. Install Dependencies

```bash
# Using UV (recommended)
uv sync

# Or using pip
pip install -r requirements.txt
```

### 2. Configure

```bash
cp .env.example .env
# Edit .env and add your API configuration
```

### 3. Start Server

```bash
# Direct run
python start_proxy.py

# Or with UV
uv run claude-code-proxy
```

### 4. Use with Claude Code

```bash
ANTHROPIC_BASE_URL=http://localhost:8082 claude
```

## Configuration

### Environment Variables

**Provider Selection:**

- `PROVIDER` - Choose your AI provider: `openai`, `azure`, or `vertex` (default: `openai`)

**OpenAI/Azure Configuration:**

- `OPENAI_API_KEY` - Your API key for OpenAI or Azure OpenAI
- `OPENAI_BASE_URL` - API base URL (default: `https://api.openai.com/v1`)
- `AZURE_API_VERSION` - Azure API version (for Azure provider)

**Vertex AI Configuration:**

- `VERTEX_PROJECT_ID` - Your Google Cloud project ID (required for Vertex AI)
- `VERTEX_LOCATION` - Vertex AI location (default: `us-central1`)

- `GOOGLE_APPLICATION_CREDENTIALS` - Path to service account key file (optional)

**Model Configuration:**

- `BIG_MODEL` - Model for Claude sonnet/opus requests
  - OpenAI/Azure default: `gpt-4o`
  - Vertex AI default: `gemini-1.5-pro`
- `SMALL_MODEL` - Model for Claude haiku requests
  - OpenAI/Azure default: `gpt-4o-mini`
  - Vertex AI default: `gemini-1.5-flash`

**Server Settings:**

- `HOST` - Server host (default: `0.0.0.0`)
- `PORT` - Server port (default: `8082`)
- `LOG_LEVEL` - Logging level (default: `WARNING`)

**Performance:**

- `MAX_TOKENS_LIMIT` - Token limit (default: `4096`)
- `MIN_TOKENS_LIMIT` - Minimum token limit (default: `1`)
- `REQUEST_TIMEOUT` - Request timeout in seconds (default: `300`)
- `MAX_RETRIES` - Maximum retry attempts (default: `3`)

### Model Mapping

The proxy maps Claude model requests to your configured models:

| Claude Request                 | Mapped To     | Environment Variable   |
| ------------------------------ | ------------- | ---------------------- |
| Models with "haiku"            | `SMALL_MODEL` | Default: `gpt-4o-mini` |
| Models with "sonnet" or "opus" | `BIG_MODEL`   | Default: `gpt-4o`      |

### Provider Examples

#### OpenAI

```bash
PROVIDER="openai"
OPENAI_API_KEY="sk-your-openai-key"
OPENAI_BASE_URL="https://api.openai.com/v1"
BIG_MODEL="gpt-4o"
SMALL_MODEL="gpt-4o-mini"
```

#### Azure OpenAI

```bash
PROVIDER="azure"
OPENAI_API_KEY="your-azure-key"
OPENAI_BASE_URL="https://your-resource.openai.azure.com/openai/deployments/your-deployment"
AZURE_API_VERSION="2024-02-15-preview"
BIG_MODEL="gpt-4"
SMALL_MODEL="gpt-35-turbo"
```

#### Google Vertex AI

```bash
PROVIDER="vertex"
VERTEX_PROJECT_ID="your-gcp-project-id"
VERTEX_LOCATION="us-central1"

# Authentication via service account key file
GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account-key.json"
# Or use gcloud CLI: gcloud auth application-default login

# For the GenAI SDK, you can also set:
GOOGLE_GENAI_USE_VERTEXAI="true"
GOOGLE_CLOUD_PROJECT="your-gcp-project-id"
GOOGLE_CLOUD_LOCATION="us-central1"

BIG_MODEL="gemini-1.5-pro"
SMALL_MODEL="gemini-1.5-flash"
```

#### Local Models (Ollama)

```bash
PROVIDER="openai"
OPENAI_API_KEY="dummy-key"  # Required but can be dummy
OPENAI_BASE_URL="http://localhost:11434/v1"
BIG_MODEL="llama3.1:70b"
SMALL_MODEL="llama3.1:8b"
```

#### Other OpenAI-Compatible Providers

Any OpenAI-compatible API can be used by setting `PROVIDER="openai"` and the appropriate `OPENAI_BASE_URL`.

### Vertex AI Setup

For Vertex AI, you need to:

1. **Enable APIs**: Enable the Vertex AI API in your Google Cloud project
2. **Authentication**: Set up authentication using one of these methods:
   - Service Account Key: Set `GOOGLE_APPLICATION_CREDENTIALS` to the path of your service account key file
   - gcloud CLI: Run `gcloud auth application-default login`
3. **IAM Permissions**: Ensure your account/service account has:
   - `Vertex AI User` role (`roles/aiplatform.user`)
   - `Service Account Token Creator` role (if using service account)

```bash
# Enable Vertex AI API
gcloud services enable aiplatform.googleapis.com

# Authenticate with gcloud CLI
gcloud auth application-default login

# Or create and download a service account key
gcloud iam service-accounts create claude-proxy-sa
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:claude-proxy-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"
gcloud iam service-accounts keys create key.json \
    --iam-account=claude-proxy-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

## Usage Examples

### Basic Chat

```python
import httpx

response = httpx.post(
    "http://localhost:8082/v1/messages",
    json={
        "model": "claude-3-5-sonnet-********",  # Maps to BIG_MODEL
        "max_tokens": 100,
        "messages": [
            {"role": "user", "content": "Hello!"}
        ]
    }
)
```

## Integration with Claude Code

This proxy is designed to work seamlessly with Claude Code CLI:

```bash
# Start the proxy
python start_proxy.py

# Use Claude Code with the proxy
ANTHROPIC_BASE_URL=http://localhost:8082 claude

# Or set permanently
export ANTHROPIC_BASE_URL=http://localhost:8082
claude
```

## Testing

Test the proxy functionality:

```bash
# Run comprehensive tests
python src/test_claude_to_openai.py

# Test Vertex AI integration specifically
python test_vertex_ai.py

# Test new GenAI SDK integration
python test_vertex_genai.py
```

### Vertex AI Quick Setup

For easy Vertex AI setup, use the automated setup script:

```bash
# Run the interactive setup script
./setup_vertex_ai.sh
```

This script will:
- Check Google Cloud CLI installation
- Authenticate with Google Cloud
- Enable required APIs
- Install Python dependencies
- Create a configured `.env` file
- Run integration tests

### Manual Testing

You can also test individual components:

```bash
# Test health endpoint
curl http://localhost:8082/health

# Test connection
curl http://localhost:8082/test-connection

# Test message endpoint
curl -X POST http://localhost:8082/v1/messages \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-********",
    "max_tokens": 100,
    "messages": [
      {"role": "user", "content": "Hello!"}
    ]
  }'
```

## Development

### Using UV

```bash
# Install dependencies
uv sync

# Run server
uv run claude-code-proxy

# Format code
uv run black src/
uv run isort src/

# Type checking
uv run mypy src/
```

### Project Structure

```
claude-code-proxy/
├── src/
│   ├── main.py  # Main server
│   ├── test_claude_to_openai.py    # Tests
│   └── [other modules...]
├── start_proxy.py                  # Startup script
├── .env.example                    # Config template
└── README.md                       # This file
```

## Performance

- **Async/await** for high concurrency
- **Connection pooling** for efficiency
- **Streaming support** for real-time responses
- **Configurable timeouts** and retries
- **Smart error handling** with detailed logging

## License

MIT License
