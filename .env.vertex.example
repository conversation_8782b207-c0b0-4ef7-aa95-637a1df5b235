# Vertex AI Provider Configuration Example
# Copy this file to .env and configure your settings

# Provider Selection (openai, azure, vertex)
PROVIDER=vertex

# Vertex AI Configuration
VERTEX_PROJECT_ID=your-gcp-project-id
VERTEX_LOCATION=us-central1

# Google Cloud Authentication
# Option 1: Service Account Key File
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json

# Option 2: Use gcloud CLI authentication (recommended for development)
# Run: gcloud auth application-default login

# For the GenAI SDK, you can also set:
GOOGLE_GENAI_USE_VERTEXAI=true
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GOOGLE_CLOUD_LOCATION=us-central1

# Server Configuration
HOST=0.0.0.0
PORT=8082
LOG_LEVEL=INFO

# Token Limits
MAX_TOKENS_LIMIT=4096
MIN_TOKENS_LIMIT=1

# Request Configuration
REQUEST_TIMEOUT=300
MAX_RETRIES=3

# Model Configuration (Vertex AI models)
BIG_MODEL=gemini-2.5-pro
SMALL_MODEL=gemini-2.5-flash

# Note: For Vertex AI, you need to:
# 1. Enable the Vertex AI API in your Google Cloud project
# 2. Set up authentication (service account or gcloud CLI)
# 3. Ensure you have the necessary IAM permissions:
#    - Vertex AI User (roles/aiplatform.user)
#    - Service Account Token Creator (if using service account)