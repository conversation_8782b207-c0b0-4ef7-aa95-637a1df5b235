[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "claude-code-proxy"
version = "1.0.0"
description = "Proxy server enabling Claude Code to work with OpenAI-compatible API providers"
readme = "README.md"
authors = [
    {name = "Claude Code Proxy", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.9"
dependencies = [
    "fastapi[standard]>=0.115.11",
    "uvicorn>=0.34.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "openai>=1.54.0",
    "aiohttp>=3.12.13",
    "google-cloud-aiplatform>=1.38.0",
    "google-auth>=2.17.0",
    "google-auth-oauthlib>=1.0.0",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "google-cloud-aiplatform>=1.38.0",
    "google-auth>=2.17.0",
    "google-auth-oauthlib>=1.0.0",
    # Dev dependencies
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0",
]

[project.urls]
Homepage = "https://github.com/holegots/claude-code-proxy"
Repository = "https://github.com/holegots/claude-code-proxy.git"
Issues = "https://github.com/holegots/claude-code-proxy/issues"

[project.scripts]
claude-code-proxy = "src.main:main"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]

[tool.black]
line-length = 100
target-version = ['py38']

[tool.isort]
profile = "black"
line_length = 100

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
