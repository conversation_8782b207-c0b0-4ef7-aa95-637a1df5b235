import asyncio
import base64
import json
import logging
import time
from typing import Op<PERSON>, AsyncGenerator, Dict, Any, List
from fastapi import HTT<PERSON>Ex<PERSON>

try:
    from google import genai
    from google.genai import types
    GENAI_AVAILABLE = True
except ImportError:
    GENAI_AVAILABLE = False
    genai = None
    types = None

class VertexAIClient:
    """Modern Vertex AI client using the unified Google GenAI SDK.
    
    This client uses the new google.genai library which unifies
    Google AI and Vertex AI APIs with a single interface.
    """
    
    def __init__(self, project_id: str, location: str = "us-central1", timeout: int = 90):
        if not GENAI_AVAILABLE:
            raise ImportError(
                "google-genai library is not installed. "
                "Please install it with: pip install google-genai>=0.3.0"
            )
        
        self.project_id = project_id
        self.location = location
        self.timeout = timeout
        self.active_requests: Dict[str, asyncio.Event] = {}
        
        # Initialize the unified GenAI client for Vertex AI
        self.client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location
        )
    
    @classmethod
    def from_environment(cls) -> "VertexAIClient":
        """Create client from environment variables.
        
        Expected environment variables:
        - GOOGLE_CLOUD_PROJECT: Your Google Cloud project ID
        - GOOGLE_CLOUD_LOCATION: Vertex AI location (default: us-central1)
        - GOOGLE_GENAI_USE_VERTEXAI: Set to 'true' to use Vertex AI
        """
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        if not project_id:
            raise ValueError("GOOGLE_CLOUD_PROJECT environment variable is required")
        
        location = os.getenv("GOOGLE_CLOUD_LOCATION", "us-central1")
        
        # Set the environment variable for the GenAI SDK
        os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "true"
        
        return cls(project_id=project_id, location=location)
    
    async def create_chat_completion(self, request: Dict[str, Any], request_id: Optional[str] = None) -> Dict[str, Any]:
        """Send chat completion to Vertex AI using the unified GenAI SDK."""
        
        # Create cancellation token if request_id provided
        if request_id:
            cancel_event = asyncio.Event()
            self.active_requests[request_id] = cancel_event
        
        try:
            # Convert OpenAI format to GenAI format
            model_name = self._map_model_name(request.get("model", "gemini-pro"))
            contents = self._convert_messages_to_contents(request.get("messages", []))
            config = self._build_generation_config(request)
            
            # Create the completion task
            completion_task = asyncio.create_task(
                self.client.aio.models.generate_content(
                    model=model_name,
                    contents=contents,
                    config=config
                )
            )
            
            if request_id:
                # Wait for either completion or cancellation
                cancel_task = asyncio.create_task(cancel_event.wait())
                done, pending = await asyncio.wait(
                    [completion_task, cancel_task],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # Cancel pending tasks
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                
                # Check if request was cancelled
                if cancel_task in done:
                    completion_task.cancel()
                    raise HTTPException(status_code=499, detail="Request cancelled by client")
                
                response = await completion_task
            else:
                response = await completion_task
            
            # Convert response to OpenAI format
            return self._convert_response_to_openai(response, request.get("model", "gemini-pro"))
        
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Vertex AI error: {str(e)}")
        
        finally:
            # Clean up active request tracking
            if request_id and request_id in self.active_requests:
                del self.active_requests[request_id]
    
    async def create_chat_completion_stream(self, request: Dict[str, Any], request_id: Optional[str] = None) -> AsyncGenerator[str, None]:
        """Send streaming chat completion to Vertex AI using the unified GenAI SDK."""
        
        # Create cancellation token if request_id provided
        if request_id:
            cancel_event = asyncio.Event()
            self.active_requests[request_id] = cancel_event
        
        try:
            # Convert OpenAI format to GenAI format
            model_name = self._map_model_name(request.get("model", "gemini-pro"))
            contents = self._convert_messages_to_contents(request.get("messages", []))
            config = self._build_generation_config(request)
            
            # Create streaming response
            stream = await self.client.aio.models.generate_content_stream(
                model=model_name,
                contents=contents,
                config=config
            )
            
            async for chunk in stream:
                # Check for cancellation before yielding each chunk
                if request_id and request_id in self.active_requests:
                    if self.active_requests[request_id].is_set():
                        raise HTTPException(status_code=499, detail="Request cancelled by client")
                
                # Convert chunk to OpenAI format
                openai_chunk = self._convert_chunk_to_openai(chunk, request.get("model", "gemini-pro"))
                chunk_json = json.dumps(openai_chunk, ensure_ascii=False)
                yield f"data: {chunk_json}\n\n"
            
            # Signal end of stream
            yield "data: [DONE]\n\n"
        
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Vertex AI streaming error: {str(e)}")
        
        finally:
            # Clean up active request tracking
            if request_id and request_id in self.active_requests:
                del self.active_requests[request_id]
    
    def _map_model_name(self, claude_model: str) -> str:
        """Map Claude model names to Vertex AI model names."""
        model_mapping = {
            "claude-3-5-sonnet-20241022": "gemini-2.0-flash-exp",
            "claude-3-5-sonnet-20240620": "gemini-2.5-pro",
            "claude-3-5-haiku-20241022": "gemini-2.5-flash",
            "claude-3-opus-20240229": "gemini-2.5-pro",
            "claude-3-sonnet-20240229": "gemini-2.5-pro",
            "claude-3-haiku-20240307": "gemini-2.5-flash",
            # Default mappings
            "claude-3-5-sonnet": "gemini-2.5-pro",
            "claude-3-sonnet": "gemini-2.5-pro",
            "claude-3-haiku": "gemini-2.5-flash",
            "claude-instant": "gemini-2.5-flash",
        }
        
        return model_mapping.get(claude_model, "gemini-2.5-pro")
    
    def _convert_messages_to_contents(self, messages: List[Dict[str, Any]]) -> List[types.Content]:
        """Convert OpenAI messages to GenAI contents."""
        contents = []
        
        for message in messages:
            role = message.get("role")
            content = message.get("content")
            
            if role == "system":
                # System messages are converted to user messages with system prefix
                contents.append(types.Content(
                    role="user",
                    parts=[types.Part(text=f"System: {content}")]
                ))
            elif role == "user":
                if isinstance(content, str) and content.strip():
                    contents.append(types.Content(
                        role="user",
                        parts=[types.Part(text=content)]
                    ))
                elif isinstance(content, list) and content:
                    parts = []
                    for part in content:
                        if part.get("type") == "text":
                            parts.append(types.Part(text=part.get("text", "")))
                        elif part.get("type") == "image_url":
                            # Handle image content
                            image_url = part.get("image_url", {}).get("url", "")
                            if image_url.startswith("data:"):
                                # Base64 encoded image
                                mime_type, data = image_url.split(",", 1)
                                mime_type = mime_type.split(":")[1].split(";")[0]
                                parts.append(types.Part(
                                     inline_data=types.Blob(
                                         data=base64.b64decode(data),
                                         mime_type=mime_type
                                     )
                                 ))
                            else:
                                # URL image
                                parts.append(types.Part(
                                     file_data=types.FileData(
                                         file_uri=image_url,
                                         mime_type="image/jpeg"  # Default
                                     )
                                 ))
                    if parts:  # Only add if there are valid parts
                        contents.append(types.Content(role="user", parts=parts))
                elif content is None or (isinstance(content, str) and not content.strip()):
                    # Handle empty or None content with a default message
                    contents.append(types.Content(
                        role="user",
                        parts=[types.Part(text="[Empty message]")]
                    ))
            elif role == "assistant":
                assistant_content = content if content else "[Empty response]"
                contents.append(types.Content(
                    role="model",
                    parts=[types.Part(text=assistant_content)]
                ))
        
        return contents
    
    def _build_generation_config(self, request: Dict[str, Any]) -> types.GenerateContentConfig:
        """Build generation config from OpenAI request parameters."""
        config_params = {}
        
        if "temperature" in request:
            config_params["temperature"] = request["temperature"]
        
        if "top_p" in request:
            config_params["top_p"] = request["top_p"]
        
        if "max_tokens" in request:
            config_params["max_output_tokens"] = request["max_tokens"]
        
        if "stop" in request:
            stop_sequences = request["stop"]
            if isinstance(stop_sequences, str):
                stop_sequences = [stop_sequences]
            config_params["stop_sequences"] = stop_sequences
        
        return types.GenerateContentConfig(**config_params)
    
    def _convert_response_to_openai(self, response, model: str) -> Dict[str, Any]:
        """Convert GenAI response to OpenAI format."""
        # Extract text content
        text_content = response.text if hasattr(response, 'text') else ""
        
        # Determine finish reason
        finish_reason = "stop"
        if hasattr(response, 'candidates') and response.candidates:
            candidate = response.candidates[0]
            if hasattr(candidate, 'finish_reason'):
                if candidate.finish_reason == "MAX_TOKENS":
                    finish_reason = "length"
                elif candidate.finish_reason == "SAFETY":
                    finish_reason = "content_filter"
        
        # Extract usage information
        usage = {
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0
        }
        
        if hasattr(response, 'usage_metadata'):
            usage_metadata = response.usage_metadata
            usage["prompt_tokens"] = getattr(usage_metadata, 'prompt_token_count', 0)
            usage["completion_tokens"] = getattr(usage_metadata, 'candidates_token_count', 0)
            usage["total_tokens"] = getattr(usage_metadata, 'total_token_count', 0)
        
        return {
            "id": f"chatcmpl-{asyncio.current_task().get_name() if asyncio.current_task() else 'unknown'}",
            "object": "chat.completion",
            "created": int(asyncio.get_event_loop().time()),
            "model": model,
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": text_content
                },
                "finish_reason": finish_reason
            }],
            "usage": usage
        }
    
    def _convert_chunk_to_openai(self, chunk, model: str) -> Dict[str, Any]:
        """Convert GenAI streaming chunk to OpenAI format."""
        # Extract delta content
        delta_content = chunk.text if hasattr(chunk, 'text') else ""
        
        # Determine finish reason
        finish_reason = None
        if hasattr(chunk, 'candidates') and chunk.candidates:
            candidate = chunk.candidates[0]
            if hasattr(candidate, 'finish_reason') and candidate.finish_reason:
                if candidate.finish_reason == "STOP":
                    finish_reason = "stop"
                elif candidate.finish_reason == "MAX_TOKENS":
                    finish_reason = "length"
                elif candidate.finish_reason == "SAFETY":
                    finish_reason = "content_filter"
        
        return {
            "id": f"chatcmpl-{asyncio.current_task().get_name() if asyncio.current_task() else 'unknown'}",
            "object": "chat.completion.chunk",
            "created": int(asyncio.get_event_loop().time()),
            "model": model,
            "choices": [{
                "index": 0,
                "delta": {
                    "content": delta_content
                } if delta_content else {},
                "finish_reason": finish_reason
            }]
        }
    
    def cancel_request(self, request_id: str) -> bool:
        """Cancel an active request by request_id."""
        if request_id in self.active_requests:
            self.active_requests[request_id].set()
            return True
        return False