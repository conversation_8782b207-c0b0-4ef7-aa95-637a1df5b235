import os
import sys

# Configuration
class Config:
    def __init__(self):
        # Provider selection
        self.provider = os.environ.get("PROVIDER", "openai").lower()  # openai, azure, vertex
        
        # OpenAI/Azure configuration
        self.openai_api_key = os.environ.get("OPENAI_API_KEY")
        self.openai_base_url = os.environ.get("OPENAI_BASE_URL", "https://api.openai.com/v1")
        self.azure_api_version = os.environ.get("AZURE_API_VERSION")  # For Azure OpenAI
        
        # Vertex AI configuration
        self.vertex_project_id = os.environ.get("VERTEX_PROJECT_ID")
        self.vertex_location = os.environ.get("VERTEX_LOCATION", "us-central1")
        
        # Validate provider-specific requirements
        if self.provider in ["openai", "azure"] and not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY not found in environment variables")
        elif self.provider == "vertex" and not self.vertex_project_id:
            raise ValueError("VERTEX_PROJECT_ID not found in environment variables for Vertex AI provider")
        
        self.host = os.environ.get("HOST", "0.0.0.0")
        self.port = int(os.environ.get("PORT", "8082"))
        self.log_level = os.environ.get("LOG_LEVEL", "INFO")
        self.max_tokens_limit = int(os.environ.get("MAX_TOKENS_LIMIT", "4096"))
        self.min_tokens_limit = int(os.environ.get("MIN_TOKENS_LIMIT", "100"))
        
        # Connection settings
        self.request_timeout = int(os.environ.get("REQUEST_TIMEOUT", "90"))
        self.max_retries = int(os.environ.get("MAX_RETRIES", "2"))
        
        # Model settings - BIG and SMALL models
        if self.provider == "vertex":
            self.big_model = os.environ.get("BIG_MODEL", "gemini-2.5-pro")
            self.small_model = os.environ.get("SMALL_MODEL", "gemini-2.5-flash")
        else:
            self.big_model = os.environ.get("BIG_MODEL", "gpt-4o")
            self.small_model = os.environ.get("SMALL_MODEL", "gpt-4o-mini")
        
    def validate_api_key(self):
        """Basic API key validation based on provider"""
        if self.provider == "vertex":
            # For Vertex AI, we rely on Google Cloud authentication
            return self.vertex_project_id is not None
        elif self.provider in ["openai", "azure"]:
            if not self.openai_api_key:
                return False
            # Basic format check for OpenAI API keys
            if not self.openai_api_key.startswith('sk-'):
                return False
            return True
        return False

try:
    config = Config()
    print(f" Configuration loaded: API_KEY={'*' * 20}..., BASE_URL='{config.openai_base_url}'")
except Exception as e:
    print(f"=4 Configuration Error: {e}")
    sys.exit(1)
