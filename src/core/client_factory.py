from typing import Union
from .config import Config
from .client import OpenAIClient
from .vertex_client import VertexAIClient

class ClientFactory:
    """Factory class to create appropriate API clients based on configuration."""
    
    @staticmethod
    def create_client(config: Config) -> Union[OpenAIClient, VertexAIClient]:
        """Create and return the appropriate client based on provider configuration."""
        
        if config.provider == "vertex":
            return VertexAIClient(
                project_id=config.vertex_project_id,
                location=config.vertex_location,
                timeout=config.request_timeout
            )
        elif config.provider in ["openai", "azure"]:
            return OpenAIClient(
                api_key=config.openai_api_key,
                base_url=config.openai_base_url,
                timeout=config.request_timeout,
                api_version=config.azure_api_version if config.provider == "azure" else None
            )
        else:
            raise ValueError(f"Unsupported provider: {config.provider}")
    
    @staticmethod
    def get_supported_providers() -> list:
        """Return list of supported providers."""
        return ["openai", "azure", "vertex"]