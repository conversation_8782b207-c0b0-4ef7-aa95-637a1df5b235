from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import JSONResponse, StreamingResponse
from datetime import datetime
import uuid

from src.core.config import config
from src.core.logging import logger
from src.core.client_factory import ClientFactory
from src.models.claude import ClaudeMessagesRequest, ClaudeTokenCountRequest
from src.conversion.request_converter import convert_claude_to_openai
from src.conversion.response_converter import (
    convert_openai_to_claude_response,
    convert_openai_streaming_to_claude_with_cancellation,
)
# Vertex converter imports removed - now using OpenAI format for Vertex AI
from src.core.model_manager import model_manager

router = APIRouter()

# 使用工厂模式创建客户端
client_factory = ClientFactory()
client = client_factory.create_client(config)

@router.post("/v1/messages")
async def create_message(request: ClaudeMessagesRequest, http_request: Request):
    try:
        logger.debug(
            f"Processing Claude request: model={request.model}, stream={request.stream}, provider={config.provider}"
        )

        # Generate unique request ID for cancellation tracking
        request_id = str(uuid.uuid4())

        # Check if client disconnected before processing
        if await http_request.is_disconnected():
            raise HTTPException(status_code=499, detail="Client disconnected")

        # 根据提供商类型选择转换器和处理逻辑
        if config.provider == "vertex":
            # Vertex AI 处理逻辑 - 使用OpenAI格式转换，让VertexAIClient内部处理
            openai_request = convert_claude_to_openai(request, model_manager)

            if request.stream:
                # Vertex AI 流式响应
                try:
                    vertex_stream = client.create_chat_completion_stream(
                        openai_request, request_id
                    )
                    
                    # 使用OpenAI流式转换器，因为VertexAIClient返回OpenAI格式的流
                    return StreamingResponse(
                        convert_openai_streaming_to_claude_with_cancellation(
                            vertex_stream,
                            request,
                            logger,
                            http_request,
                            client,
                            request_id,
                        ),
                        media_type="text/event-stream",
                        headers={
                            "Cache-Control": "no-cache",
                            "Connection": "keep-alive",
                            "Access-Control-Allow-Origin": "*",
                            "Access-Control-Allow-Headers": "*",
                        },
                    )
                except HTTPException as e:
                    logger.error(f"Vertex AI streaming error: {e.detail}")
                    import traceback
                    logger.error(traceback.format_exc())
                    error_message = client.classify_vertex_error(e.detail) if hasattr(client, 'classify_vertex_error') else str(e.detail)
                    error_response = {
                        "type": "error",
                        "error": {"type": "api_error", "message": error_message},
                    }
                    return JSONResponse(status_code=e.status_code, content=error_response)
            else:
                # Vertex AI 非流式响应
                openai_response = await client.create_chat_completion(
                    openai_request, request_id
                )
                claude_response = convert_openai_to_claude_response(
                    openai_response, request
                )
                # Check minimum tokens limit
                if "usage" in claude_response and "output_tokens" in claude_response["usage"]:
                    output_tokens = claude_response["usage"]["output_tokens"]
                    if output_tokens < config.min_tokens_limit:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Output tokens ({output_tokens}) is less than minimum limit ({config.min_tokens_limit}))",
                        )
                return claude_response
        else:
            # OpenAI/Azure 处理逻辑（原有逻辑）
            openai_request = convert_claude_to_openai(request, model_manager)
            
            if request.stream:
                # OpenAI 流式响应
                try:
                    openai_stream = client.create_chat_completion_stream(
                        openai_request, request_id
                    )
                    return StreamingResponse(
                        convert_openai_streaming_to_claude_with_cancellation(
                            openai_stream,
                            request,
                            logger,
                            http_request,
                            client,
                            request_id,
                        ),
                        media_type="text/event-stream",
                        headers={
                            "Cache-Control": "no-cache",
                            "Connection": "keep-alive",
                            "Access-Control-Allow-Origin": "*",
                            "Access-Control-Allow-Headers": "*",
                        },
                    )
                except HTTPException as e:
                    logger.error(f"OpenAI streaming error: {e.detail}")
                    import traceback
                    logger.error(traceback.format_exc())
                    error_message = client.classify_openai_error(e.detail) if hasattr(client, 'classify_openai_error') else str(e.detail)
                    error_response = {
                        "type": "error",
                        "error": {"type": "api_error", "message": error_message},
                    }
                    return JSONResponse(status_code=e.status_code, content=error_response)
            else:
                # OpenAI 非流式响应
                openai_response = await client.create_chat_completion(
                    openai_request, request_id
                )
                claude_response = convert_openai_to_claude_response(
                    openai_response, request
                )
                # Check minimum tokens limit
                if "usage" in claude_response and "output_tokens" in claude_response["usage"]:
                    output_tokens = claude_response["usage"]["output_tokens"]
                    if output_tokens < config.min_tokens_limit:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Output tokens ({output_tokens}) is less than minimum limit ({config.min_tokens_limit}))",
                        )
                return claude_response
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        logger.error(f"Unexpected error processing request: {e}")
        logger.error(traceback.format_exc())
        error_message = str(e)
        if hasattr(client, 'classify_openai_error'):
            error_message = client.classify_openai_error(str(e))
        elif hasattr(client, 'classify_vertex_error'):
            error_message = client.classify_vertex_error(str(e))
        raise HTTPException(status_code=500, detail=error_message)


@router.post("/v1/messages/count_tokens")
async def count_tokens(request: ClaudeTokenCountRequest):
    try:
        # For token counting, we'll use a simple estimation
        # In a real implementation, you might want to use tiktoken or similar

        total_chars = 0

        # Count system message characters
        if request.system:
            if isinstance(request.system, str):
                total_chars += len(request.system)
            elif isinstance(request.system, list):
                for block in request.system:
                    if hasattr(block, "text"):
                        total_chars += len(block.text)

        # Count message characters
        for msg in request.messages:
            if msg.content is None:
                continue
            elif isinstance(msg.content, str):
                total_chars += len(msg.content)
            elif isinstance(msg.content, list):
                for block in msg.content:
                    if hasattr(block, "text") and block.text is not None:
                        total_chars += len(block.text)

        # Rough estimation: 4 characters per token
        estimated_tokens = max(1, total_chars // 4)

        return {"input_tokens": estimated_tokens}

    except Exception as e:
        logger.error(f"Error counting tokens: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    health_info = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "provider": config.provider,
        "api_key_valid": config.validate_api_key(),
    }
    
    # 根据提供商添加特定的配置信息
    if config.provider == "vertex":
        health_info.update({
            "vertex_project_configured": bool(config.vertex_project_id),
            "vertex_location": config.vertex_location,
        })
    else:
        health_info.update({
            "api_key_configured": bool(config.openai_api_key),
            "base_url": config.openai_base_url,
        })
    
    return health_info


@router.get("/test-connection")
async def test_connection():
    """Test API connectivity to configured provider"""
    try:
        # Simple test request to verify API connectivity
        if config.provider == "vertex":
            # Vertex AI 测试请求 - 使用OpenAI格式
            test_request = {
                "model": config.small_model,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 5,
                "temperature": 0.1
            }
            test_response = await client.create_chat_completion(test_request)
            
            return {
                "status": "success",
                "message": f"Successfully connected to Vertex AI (Project: {config.vertex_project_id})",
                "provider": config.provider,
                "model_used": config.small_model,
                "timestamp": datetime.now().isoformat(),
                "response_id": test_response.get("id", "unknown"),
            }
        else:
            # OpenAI/Azure 测试请求
            test_request = {
                "model": config.small_model,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 5,
            }
            test_response = await client.create_chat_completion(test_request)
            
            return {
                "status": "success",
                "message": f"Successfully connected to {config.provider.upper()} API",
                "provider": config.provider,
                "model_used": config.small_model,
                "timestamp": datetime.now().isoformat(),
                "response_id": test_response.get("id", "unknown"),
            }

    except Exception as e:
        logger.error(f"API connectivity test failed: {e}")
        
        # 根据提供商类型提供不同的建议
        suggestions = []
        if config.provider == "vertex":
            suggestions = [
                "Check your Google Cloud credentials are properly configured",
                "Verify your project ID and location are correct",
                "Ensure Vertex AI API is enabled in your project",
                "Check if you have the necessary IAM permissions",
            ]
        else:
            suggestions = [
                f"Check your {config.provider.upper()}_API_KEY is valid",
                "Verify your API key has the necessary permissions",
                "Check if you have reached rate limits",
            ]
        
        return JSONResponse(
            status_code=503,
            content={
                "status": "failed",
                "provider": config.provider,
                "error_type": "API Error",
                "message": str(e),
                "timestamp": datetime.now().isoformat(),
                "suggestions": suggestions,
            },
        )


@router.get("/")
async def root():
    """Root endpoint"""
    config_info = {
        "provider": config.provider,
        "max_tokens_limit": config.max_tokens_limit,
        "big_model": config.big_model,
        "small_model": config.small_model,
    }
    
    # 根据提供商添加特定配置信息
    if config.provider == "vertex":
        config_info.update({
            "vertex_project_id": config.vertex_project_id,
            "vertex_location": config.vertex_location,
            "project_configured": bool(config.vertex_project_id),
        })
    else:
        config_info.update({
            "base_url": config.openai_base_url,
            "api_key_configured": bool(config.openai_api_key),
        })
    
    return {
        "message": "Claude API Proxy v2.0.0 - Multi-Provider Support",
        "status": "running",
        "supported_providers": client_factory.get_supported_providers(),
        "current_provider": config.provider,
        "config": config_info,
        "endpoints": {
            "messages": "/v1/messages",
            "count_tokens": "/v1/messages/count_tokens",
            "health": "/health",
            "test_connection": "/test-connection",
        },
    }
