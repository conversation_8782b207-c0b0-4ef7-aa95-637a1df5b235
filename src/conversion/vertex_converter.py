from typing import Dict, Any, List, Optional
import json
import asyncio
from fastapi.responses import StreamingResponse
from ..models.claude import Claude<PERSON>essagesRequest, <PERSON><PERSON><PERSON><PERSON>

def convert_claude_to_vertex(claude_request: ClaudeMessagesRequest, model_manager) -> Dict[str, Any]:
    """Convert Claude API request to Vertex AI format."""
    
    # Map Claude model to Vertex AI model using ModelManager
    claude_model = claude_request.model
    vertex_model = model_manager.map_claude_model_to_openai(claude_model)
    
    # Convert messages to Vertex AI format
    contents = []
    system_instruction = None
    
    for message in claude_request.messages:
        if message.role == "system":
            # Vertex AI handles system instructions separately
            system_instruction = message.content[0].text if message.content else ""
        elif message.role == "user":
            contents.append(_convert_claude_user_message_to_vertex(message))
        elif message.role == "assistant":
            contents.append(_convert_claude_assistant_message_to_vertex(message))
    
    # Build Vertex AI request
    vertex_request = {
        "contents": contents,
        "generationConfig": {
            "temperature": claude_request.temperature if claude_request.temperature is not None else 0.7,
            "topP": claude_request.top_p if claude_request.top_p is not None else 1.0,
            "maxOutputTokens": claude_request.max_tokens if claude_request.max_tokens else 1024
        }
    }
    
    # Add system instruction if present
    if system_instruction:
        vertex_request["systemInstruction"] = {
            "parts": [{"text": system_instruction}]
        }
    
    # Handle stop sequences
    if claude_request.stop_sequences:
        vertex_request["generationConfig"]["stopSequences"] = claude_request.stop_sequences
    
    # Handle tools if present
    if claude_request.tools:
        vertex_request["tools"] = _convert_claude_tools_to_vertex(claude_request.tools)
    
    # Add model to request for reference
    vertex_request["_model"] = vertex_model
    
    return vertex_request

def _convert_claude_user_message_to_vertex(message: ClaudeMessage) -> Dict[str, Any]:
    """Convert Claude user message to Vertex AI format."""
    parts = []
    
    # Handle case where content is None
    if message.content is None:
        return {"role": "user", "parts": [{"text": "[Empty message]"}]}
    
    # Handle case where content is a string
    if isinstance(message.content, str):
        # Check for empty or whitespace-only strings
        content_text = message.content.strip() if message.content else ""
        if not content_text:
            content_text = "[Empty message]"
        return {"role": "user", "parts": [{"text": content_text}]}
    
    # Handle multimodal content (list of content blocks)
    for content in message.content:
        if content.type == "text":
            # Validate text content is not empty
            text_content = content.text.strip() if content.text else ""
            if text_content:
                parts.append({"text": text_content})
        elif content.type == "image":
            # Handle image content
            if hasattr(content, 'source') and content.source:
                if content.source.type == "base64":
                    parts.append({
                        "inlineData": {
                            "mimeType": content.source.media_type,
                            "data": content.source.data
                        }
                    })
    
    # Ensure parts is not empty - add default text if no valid content found
    if not parts:
        parts = [{"text": "[Empty message]"}]
    
    return {
        "role": "user",
        "parts": parts
    }

def _convert_claude_assistant_message_to_vertex(message: ClaudeMessage) -> Dict[str, Any]:
    """Convert Claude assistant message to Vertex AI format."""
    parts = []
    
    # Handle case where content is None
    if message.content is None:
        return {"role": "model", "parts": [{"text": "[Empty response]"}]}
    
    # Handle case where content is a string
    if isinstance(message.content, str):
        # Check for empty or whitespace-only strings
        content_text = message.content.strip() if message.content else ""
        if not content_text:
            content_text = "[Empty response]"
        return {"role": "model", "parts": [{"text": content_text}]}
    
    # Handle multimodal content (list of content blocks)
    for content in message.content:
        if content.type == "text":
            # Validate text content is not empty
            text_content = content.text.strip() if content.text else ""
            if text_content:
                parts.append({"text": text_content})
        elif content.type == "tool_use":
            # Convert tool use to Vertex AI function call format
            parts.append({
                "functionCall": {
                    "name": content.name,
                    "args": content.input
                }
            })
    
    # Ensure parts is not empty - add default text if no valid content found
    if not parts:
        parts = [{"text": "[Empty response]"}]
    
    return {
        "role": "model",
        "parts": parts
    }

def _convert_claude_tools_to_vertex(claude_tools: List[Any]) -> List[Dict[str, Any]]:
    """Convert Claude tools to Vertex AI function declarations."""
    vertex_tools = []
    
    for tool in claude_tools:
        # ClaudeTool is a Pydantic model with name, description, input_schema attributes
        vertex_function = {
            "functionDeclarations": [{
                "name": tool.name,
                "description": tool.description or "",
                "parameters": tool.input_schema
            }]
        }
        vertex_tools.append(vertex_function)
    
    return vertex_tools

def convert_vertex_to_claude_response(vertex_response: Dict[str, Any], claude_model: str, request_id: str) -> Dict[str, Any]:
    """Convert Vertex AI response to Claude API format."""
    
    candidates = vertex_response.get("candidates", [])
    
    if not candidates:
        return {
            "id": request_id,
            "type": "message",
            "role": "assistant",
            "model": claude_model,
            "content": [],
            "stop_reason": "end_turn",
            "stop_sequence": None,
            "usage": {
                "input_tokens": 0,
                "output_tokens": 0
            }
        }
    
    # Process first candidate
    candidate = candidates[0]
    content_parts = candidate.get("content", {}).get("parts", [])
    
    # Convert content parts to Claude format
    claude_content = []
    for part in content_parts:
        if "text" in part:
            claude_content.append({
                "type": "text",
                "text": part["text"]
            })
        elif "functionCall" in part:
            # Convert function call to Claude tool_use format
            function_call = part["functionCall"]
            claude_content.append({
                "type": "tool_use",
                "id": f"toolu_{request_id}_{len(claude_content)}",
                "name": function_call.get("name", ""),
                "input": function_call.get("args", {})
            })
    
    # Determine stop reason
    stop_reason = "end_turn"
    finish_reason = candidate.get("finishReason")
    if finish_reason == "MAX_TOKENS":
        stop_reason = "max_tokens"
    elif finish_reason == "STOP":
        stop_reason = "end_turn"
    elif finish_reason == "SAFETY":
        stop_reason = "stop_sequence"
    
    # Extract usage information
    usage_metadata = vertex_response.get("usageMetadata", {})
    
    return {
        "id": request_id,
        "type": "message",
        "role": "assistant",
        "model": claude_model,
        "content": claude_content,
        "stop_reason": stop_reason,
        "stop_sequence": None,
        "usage": {
            "input_tokens": usage_metadata.get("promptTokenCount", 0),
            "output_tokens": usage_metadata.get("candidatesTokenCount", 0)
        }
    }

async def convert_vertex_streaming_to_claude(
    vertex_stream,
    original_request,
    logger,
    http_request,
    vertex_client,
    request_id: str,
):
    """Convert Vertex AI streaming response to Claude streaming format."""
    
    # Send message_start event
    message_start = {
        "type": "message_start",
        "message": {
            "id": request_id,
            "type": "message",
            "role": "assistant",
            "model": original_request.model,
            "content": [],
            "stop_reason": None,
            "stop_sequence": None,
            "usage": {"input_tokens": 0, "output_tokens": 0}
        }
    }
    yield f"event: message_start\ndata: {json.dumps(message_start)}\n\n"
    
    content_block_index = 0
    current_tool_use = None
    accumulated_text = ""
    
    async for chunk in vertex_stream:
        try:
            if isinstance(chunk, str):
                if chunk.startswith('data: '):
                    chunk_data = chunk[6:]  # Remove 'data: ' prefix
                    if chunk_data == '[DONE]':
                        break
                    vertex_chunk = json.loads(chunk_data)
                else:
                    continue
            else:
                vertex_chunk = chunk
            
            candidates = vertex_chunk.get("candidates", [])
            if not candidates:
                continue
            
            candidate = candidates[0]
            content_parts = candidate.get("content", {}).get("parts", [])
            
            for part in content_parts:
                if "text" in part:
                    text_delta = part["text"]
                    
                    if not accumulated_text:  # First text chunk
                        # Send content_block_start
                        content_block_start = {
                            "type": "content_block_start",
                            "index": content_block_index,
                            "content_block": {
                                "type": "text",
                                "text": ""
                            }
                        }
                        yield f"event: content_block_start\ndata: {json.dumps(content_block_start)}\n\n"
                    
                    # Send content_block_delta
                    content_block_delta = {
                        "type": "content_block_delta",
                        "index": content_block_index,
                        "delta": {
                            "type": "text_delta",
                            "text": text_delta
                        }
                    }
                    yield f"event: content_block_delta\ndata: {json.dumps(content_block_delta)}\n\n"
                    
                    accumulated_text += text_delta
                
                elif "functionCall" in part:
                    function_call = part["functionCall"]
                    
                    if not current_tool_use:  # Start of tool use
                        current_tool_use = {
                            "type": "tool_use",
                            "id": f"toolu_{request_id}_{content_block_index}",
                            "name": function_call.get("name", ""),
                            "input": {}
                        }
                        
                        # Send content_block_start for tool use
                        content_block_start = {
                            "type": "content_block_start",
                            "index": content_block_index,
                            "content_block": current_tool_use
                        }
                        yield f"event: content_block_start\ndata: {json.dumps(content_block_start)}\n\n"
                    
                    # Update tool use input
                    args = function_call.get("args", {})
                    current_tool_use["input"].update(args)
                    
                    # Send content_block_delta for tool use
                    content_block_delta = {
                        "type": "content_block_delta",
                        "index": content_block_index,
                        "delta": {
                            "type": "input_json_delta",
                            "partial_json": json.dumps(args)
                        }
                    }
                    yield f"event: content_block_delta\ndata: {json.dumps(content_block_delta)}\n\n"
            
            # Check for finish reason
            finish_reason = candidate.get("finishReason")
            if finish_reason:
                # Send content_block_stop
                content_block_stop = {
                    "type": "content_block_stop",
                    "index": content_block_index
                }
                yield f"event: content_block_stop\ndata: {json.dumps(content_block_stop)}\n\n"
                
                # Determine Claude stop reason
                claude_stop_reason = "end_turn"
                if finish_reason == "MAX_TOKENS":
                    claude_stop_reason = "max_tokens"
                elif finish_reason == "STOP":
                    claude_stop_reason = "end_turn"
                elif finish_reason == "SAFETY":
                    claude_stop_reason = "stop_sequence"
                
                # Send message_delta with stop reason
                message_delta = {
                    "type": "message_delta",
                    "delta": {
                        "stop_reason": claude_stop_reason,
                        "stop_sequence": None
                    },
                    "usage": {
                        "output_tokens": vertex_chunk.get("usageMetadata", {}).get("candidatesTokenCount", 0)
                    }
                }
                yield f"event: message_delta\ndata: {json.dumps(message_delta)}\n\n"
                
                # Send message_stop
                message_stop = {"type": "message_stop"}
                yield f"event: message_stop\ndata: {json.dumps(message_stop)}\n\n"
                break
        
        except json.JSONDecodeError as e:
            logger.warning(f"JSON decode error in Vertex AI streaming: {e}")
            continue
        except Exception as e:
            # Log error but continue processing to avoid breaking the stream
            logger.error(f"Error processing Vertex AI chunk: {e}")
            # Send error event to client if possible
            try:
                error_event = {
                    "type": "error",
                    "error": {
                        "type": "api_error",
                        "message": "Internal processing error"
                    }
                }
                yield f"event: error\ndata: {json.dumps(error_event)}\n\n"
            except:
                pass  # If we can't send error event, just continue
            continue
    
    # Send ping events periodically
    ping_event = {"type": "ping"}
    yield f"event: ping\ndata: {json.dumps(ping_event)}\n\n"